#!/usr/bin/env python3
"""单日循环模拟使用示例

演示如何使用DailySimulationManager进行单日循环模拟：
1. 按照您的需求，每次运行一天
2. 前一天的输出状态作为下一天的输入
3. 使用HDF5存储和管理状态数据

使用方法：
python daily_simulation_example.py
"""

import datetime
import logging
from daily_simulation import DailySimulationManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_simulation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def main():
    """主函数 - 演示单日循环模拟"""

    print("=== pyAHC 单日循环模拟示例 ===")

    # 1. 配置模拟参数
    config = {
        'project_name': 'hetao_daily_2013',
        'hdf5_path': './daily_simulation_2013.h5',
        'output_dir': './temp_daily_sim',
        'latitude': 45.75,
        'altitude': 1039.3
    }

    print(f"项目名称: {config['project_name']}")
    print(f"HDF5文件: {config['hdf5_path']}")
    print(f"输出目录: {config['output_dir']}")

    # 2. 创建模拟管理器
    try:
        sim_manager = DailySimulationManager(config)
        print("✓ 模拟管理器创建成功")
    except Exception as e:
        print(f"✗ 模拟管理器创建失败: {e}")
        return 1

    # 3. 按照您的需求运行单日模拟
    simulation_dates = [
        datetime.date(2013, 5, 2),
        datetime.date(2013, 5, 3),
        datetime.date(2013, 5, 4),
        datetime.date(2013, 5, 5),
        datetime.date(2013, 5, 6),
    ]

    print(f"\n开始运行 {len(simulation_dates)} 天的单日循环模拟...")

    # 4. 初始化项目
    try:
        sim_manager.initialize_project()
        print("✓ 项目初始化成功")
    except Exception as e:
        print(f"✗ 项目初始化失败: {e}")
        return 1

    # 5. 逐日运行模拟
    success_count = 0
    for i, current_date in enumerate(simulation_dates, 1):
        print(f"\n--- 第 {i} 天模拟: {current_date} ---")

        try:
            success = sim_manager.run_single_day_simulation(current_date)

            if success:
                success_count += 1
                print(f"✓ 第 {i} 天模拟成功: {current_date}")

                # 可选：显示状态信息
                try:
                    with sim_manager.hdf5_manager:
                        daily_data = sim_manager.hdf5_manager.load_daily_data(
                            sim_manager.project_name, current_date
                        )
                        states = daily_data.get('states', {})
                        print(f"  保存的状态变量: {list(states.keys())}")
                except Exception as e:
                    print(f"  状态信息获取失败: {e}")

            else:
                print(f"✗ 第 {i} 天模拟失败: {current_date}")

        except Exception as e:
            print(f"✗ 第 {i} 天模拟异常: {current_date}, 错误: {e}")
            logger.exception(f"模拟异常详情: {current_date}")

    # 6. 总结结果
    print(f"\n=== 模拟完成 ===")
    print(f"总天数: {len(simulation_dates)}")
    print(f"成功天数: {success_count}")
    print(f"失败天数: {len(simulation_dates) - success_count}")
    print(f"成功率: {success_count/len(simulation_dates)*100:.1f}%")

    if success_count == len(simulation_dates):
        print("✓ 所有模拟均成功完成！")
        return 0
    else:
        print("✗ 部分模拟失败")
        return 1


def run_continuous_example():
    """运行连续模拟示例"""

    print("=== pyAHC 连续日循环模拟示例 ===")

    # 配置参数
    config = {
        'project_name': 'hetao_continuous_2013',
        'hdf5_path': './continuous_simulation_2013.h5',
        'output_dir': './temp_continuous_sim',
        'latitude': 45.75,
        'altitude': 1039.3
    }

    # 创建模拟管理器
    sim_manager = DailySimulationManager(config)

    # 运行连续模拟
    start_date = datetime.date(2013, 5, 2)
    end_date = datetime.date(2013, 5, 10)  # 模拟9天

    print(f"运行连续模拟: {start_date} 到 {end_date}")

    success = sim_manager.run_daily_sequence(start_date, end_date)

    if success:
        print("✓ 连续日循环模拟成功完成")
        return 0
    else:
        print("✗ 连续日循环模拟失败")
        return 1


if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 单日循环模拟（按您的需求）")
    print("2. 连续日循环模拟")

    choice = input("请输入选择 (1/2，默认1): ").strip()

    if choice == "2":
        exit_code = run_continuous_example()
    else:
        exit_code = main()

    exit(exit_code)