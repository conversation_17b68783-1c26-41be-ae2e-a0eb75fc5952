# 优化配置模板
# 用于高性能长期模拟的配置设置

# 基础项目配置
project:
  name: "optimized_simulation"
  description: "High-performance optimized simulation"
  
# HDF5存储配置
storage:
  hdf5_path: "optimized_simulation.h5"
  compression: "gzip"
  compression_level: 6
  chunk_size: 1000
  
# 模拟时间配置
simulation:
  start_date: "2023-05-01"
  end_date: "2023-09-30"
  
# 地理位置配置
location:
  field_id: "field_001"
  latitude: 45.75
  longitude: -93.22
  altitude: 300.0
  
# 作物配置
crop:
  type: "corn"
  variety: "standard"
  planting_date: "2023-05-01"
  
# 性能优化配置
optimization:
  # 内存管理
  memory_limit_mb: 2048
  gc_frequency: 10  # 每N天执行垃圾回收
  
  # 检查点配置
  checkpoint_frequency: 50  # 每N天保存检查点
  checkpoint_dir: "./checkpoints"
  
  # 并行处理
  enable_parallel: false
  max_workers: 2
  
  # 错误处理
  stop_on_error: true
  
# 批量模拟配置
batch:
  enable_parallel_batch: false
  max_batch_workers: 4
  
# 监控配置
monitoring:
  enable_performance_monitoring: true
  log_level: "INFO"
  progress_report_frequency: 10  # 每N天报告进度
  
# 数据同化配置（可选）
assimilation:
  enable_assimilation: false
  
# 高级优化选项
advanced:
  # 状态缓存
  state_cache_size: 10  # 缓存最近N天的状态
  
  # 异步保存
  async_save: false
  
  # 内存优化
  smart_copy: true  # 使用智能模型拷贝
  
  # 数据压缩
  compress_states: true
  compress_results: true
