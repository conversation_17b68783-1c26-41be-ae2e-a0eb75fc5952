# 批量模拟配置模板
# 适用于参数扫描和批量处理

# 基础项目配置
project:
  name: "batch_simulation"
  description: "Batch simulation with parameter sweep"
  
# HDF5存储配置
storage:
  hdf5_path: "batch_simulation.h5"
  compression: "gzip"
  compression_level: 6
  chunk_size: 1000
  
# 模拟时间配置（较短的模拟用于快速批量处理）
simulation:
  start_date: "2023-05-01"
  end_date: "2023-08-31"
  
# 地理位置配置
location:
  field_id: "field_batch"
  latitude: 42.0
  longitude: -90.0
  altitude: 200.0
  
# 作物配置
crop:
  type: "corn"
  variety: "standard"
  planting_date: "2023-05-01"
  
# 性能优化配置（针对批量处理优化）
optimization:
  # 内存管理
  memory_limit_mb: 3072  # 更高的内存限制用于并行处理
  gc_frequency: 15  # 较少的垃圾回收频率
  
  # 检查点配置（批量处理通常不需要频繁检查点）
  checkpoint_frequency: 100
  checkpoint_dir: "./checkpoints_batch"
  
  # 并行处理（启用并行以加速批量处理）
  enable_parallel: true
  max_workers: 4
  
  # 错误处理
  stop_on_error: false  # 批量处理中单个失败不应停止整个过程
  
# 批量模拟配置（核心配置）
batch:
  enable_parallel_batch: true
  max_batch_workers: 8  # 高并行度
  
  # 参数扫描配置
  parameter_sweep:
    # 示例参数范围
    irrigation_efficiency: [0.7, 0.8, 0.9, 1.0]
    fertilizer_rate: [100, 150, 200, 250]  # kg/ha
    planting_density: [70000, 80000, 90000]  # plants/ha
    
  # 批量处理选项
  batch_size: 10  # 每批处理的模拟数量
  retry_failed: true
  max_retries: 2
  
# 监控配置
monitoring:
  enable_performance_monitoring: true
  log_level: "INFO"
  progress_report_frequency: 5  # 更频繁的进度报告
  
  # 批量监控特有配置
  track_individual_simulations: true
  generate_batch_summary: true
  
# 数据同化配置
assimilation:
  enable_assimilation: false
  
# 高级优化选项
advanced:
  # 状态缓存（批量处理中缓存不太重要）
  state_cache_size: 3
  
  # 异步保存（启用以提高并行性能）
  async_save: true
  
  # 内存优化
  smart_copy: true
  
  # 数据压缩
  compress_states: true
  compress_results: false  # 批量处理中可能需要快速访问结果
  
  # 批量处理特有配置
  parallel_io: true  # 并行I/O
  shared_memory: false  # 是否使用共享内存
  
# 结果处理配置
results:
  # 自动分析
  auto_analysis: true
  generate_comparison: true
  
  # 输出格式
  export_csv: true
  export_plots: true
  
  # 统计分析
  calculate_statistics: true
  sensitivity_analysis: true
  
# 资源管理
resources:
  # CPU使用
  max_cpu_percent: 80
  
  # 内存使用
  max_total_memory: 8192  # MB
  
  # 磁盘空间
  max_disk_space: 50000  # MB
  cleanup_intermediate: true
