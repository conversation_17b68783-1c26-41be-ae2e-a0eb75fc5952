# 长期模拟配置模板
# 适用于1000+天的大规模长期模拟

# 基础项目配置
project:
  name: "long_term_simulation"
  description: "Long-term simulation (1000+ days)"
  
# HDF5存储配置
storage:
  hdf5_path: "long_term_simulation.h5"
  compression: "gzip"
  compression_level: 9  # 最高压缩率节省空间
  chunk_size: 2000
  
# 模拟时间配置（3年模拟）
simulation:
  start_date: "2020-01-01"
  end_date: "2022-12-31"
  
# 地理位置配置
location:
  field_id: "field_longterm"
  latitude: 40.0
  longitude: -95.0
  altitude: 250.0
  
# 作物配置
crop:
  type: "corn"
  variety: "drought_resistant"
  planting_date: "2020-05-01"
  
# 性能优化配置（针对长期模拟优化）
optimization:
  # 内存管理（更严格的内存控制）
  memory_limit_mb: 1536  # 较低的内存限制
  gc_frequency: 5  # 更频繁的垃圾回收
  
  # 检查点配置（更频繁的检查点）
  checkpoint_frequency: 30  # 每30天保存检查点
  checkpoint_dir: "./checkpoints_longterm"
  
  # 并行处理
  enable_parallel: false  # 长期模拟建议关闭并行以保证稳定性
  max_workers: 1
  
  # 错误处理
  stop_on_error: false  # 长期模拟建议继续执行
  
# 批量模拟配置
batch:
  enable_parallel_batch: false
  max_batch_workers: 2  # 减少并行数量
  
# 监控配置
monitoring:
  enable_performance_monitoring: true
  log_level: "WARNING"  # 减少日志输出
  progress_report_frequency: 50  # 每50天报告进度
  
# 数据同化配置
assimilation:
  enable_assimilation: false
  
# 高级优化选项（针对长期模拟）
advanced:
  # 状态缓存（减少缓存大小）
  state_cache_size: 5
  
  # 异步保存
  async_save: false  # 同步保存确保数据完整性
  
  # 内存优化
  smart_copy: true
  
  # 数据压缩（最大压缩）
  compress_states: true
  compress_results: true
  
  # 长期模拟特有配置
  auto_cleanup: true  # 自动清理临时文件
  memory_check_frequency: 1  # 每天检查内存使用
  
# 资源限制
resources:
  max_memory_per_day: 100  # MB
  max_disk_space: 10000  # MB
  cleanup_old_checkpoints: true
  keep_checkpoints_days: 100  # 只保留最近100天的检查点
