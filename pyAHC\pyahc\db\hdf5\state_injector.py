"""状态变量注入器"""
import numpy as np
from typing import Dict, Any, Optional, TYPE_CHECKING
import logging
import copy

if TYPE_CHECKING:
    from pyahc.model.model import Model


class StateInjector:
    """将状态变量注入到模型输入中"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    @staticmethod
    def inject_all_states(model: 'Model', states: Dict[str, Any]) -> 'Model':
        """注入所有状态变量

        Args:
            model: 原始模型对象
            states: 状态变量字典

        Returns:
            注入状态后的新模型对象
        """
        try:
            # 创建模型的深拷贝，避免修改原始对象
            updated_model = model.model_copy(deep=True)

            # 验证输入状态
            validated_states = StateInjector._validate_input_states(states)

            # 注入土壤含水量
            if 'soil_moisture' in validated_states:
                updated_model = StateInjector.inject_soil_moisture(
                    updated_model, validated_states['soil_moisture']
                )

            # 注入作物状态
            crop_states = {
                'lai': validated_states.get('lai'),
                'biomass': validated_states.get('biomass'),
                'root_depth': validated_states.get('root_depth')
            }
            if any(v is not None for v in crop_states.values()):
                updated_model = StateInjector.inject_crop_state(
                    updated_model, **crop_states
                )

            # 注入地下水位
            if 'groundwater_level' in validated_states:
                updated_model = StateInjector.inject_groundwater_level(
                    updated_model, validated_states['groundwater_level']
                )

            # 验证注入结果
            StateInjector._validate_injection_result(updated_model, validated_states)

            logging.info(f"Successfully injected {len(validated_states)} state variables")
            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject states: {e}")
            # 返回原始模型作为回退
            return model

    @staticmethod
    def _validate_input_states(states: Dict[str, Any]) -> Dict[str, Any]:
        """验证输入状态变量"""
        validated_states = {}

        for var_name, value in states.items():
            if value is not None:
                try:
                    validated_value = StateInjector._validate_state_value(var_name, value)
                    if validated_value is not None:
                        validated_states[var_name] = validated_value
                except Exception as e:
                    logging.warning(f"Failed to validate state {var_name}: {e}")

        return validated_states

    @staticmethod
    def _validate_state_value(var_name: str, value: Any) -> Optional[Any]:
        """验证单个状态变量值"""
        if var_name == 'soil_moisture':
            if isinstance(value, (list, tuple)):
                value = np.array(value)
            if isinstance(value, np.ndarray):
                # 确保值在合理范围内
                if np.all((value >= 0) & (value <= 1)):
                    return value.astype(np.float64)
                else:
                    logging.warning(f"Soil moisture values out of range, clipping to [0,1]")
                    return np.clip(value, 0, 1).astype(np.float64)

        elif var_name in ['lai', 'biomass', 'root_depth', 'groundwater_level']:
            if isinstance(value, (int, float, np.number)):
                float_value = float(value)
                # 添加合理性检查
                if var_name == 'lai' and float_value <= 0:
                    logging.warning(f"LAI value {float_value} is not positive, setting to 0.1")
                    return 0.1  # 设置为最小正值
                elif var_name in ['biomass', 'root_depth'] and float_value < 0:
                    logging.warning(f"{var_name} value {float_value} is negative, setting to 0")
                    return 0.0
                return float_value
            elif isinstance(value, str):
                try:
                    float_value = float(value)
                    # 同样的合理性检查
                    if var_name == 'lai' and float_value <= 0:
                        logging.warning(f"LAI value {float_value} is not positive, setting to 0.1")
                        return 0.1  # 设置为最小正值
                    elif var_name in ['biomass', 'root_depth'] and float_value < 0:
                        logging.warning(f"{var_name} value {float_value} is negative, setting to 0")
                        return 0.0
                    return float_value
                except ValueError:
                    logging.warning(f"Cannot convert {var_name} value '{value}' to float, skipping")
                    return None

        return value

    @staticmethod
    def inject_soil_moisture(model: 'Model', moisture: np.ndarray) -> 'Model':
        """注入土壤含水量

        Args:
            model: 模型对象
            moisture: 土壤含水量数组

        Returns:
            更新后的模型对象
        """
        try:
            updated_model = model.model_copy(deep=True)

            # 检查模型是否有土壤含水量组件
            if not hasattr(updated_model, 'soilmoisture') or updated_model.soilmoisture is None:
                logging.warning("Model does not have soilmoisture component")
                return updated_model

            # 转换数据格式
            if isinstance(moisture, np.ndarray):
                moisture_list = moisture.tolist()
            else:
                moisture_list = list(moisture)

            # 注入到模型中
            if hasattr(updated_model.soilmoisture, 'thetai'):
                # 检查层数是否匹配
                if hasattr(updated_model.soilmoisture.thetai, '__len__'):
                    expected_layers = len(updated_model.soilmoisture.thetai)
                    if len(moisture_list) != expected_layers:
                        logging.warning(f"Moisture layers mismatch: got {len(moisture_list)}, expected {expected_layers}")
                        # 调整数组大小
                        moisture_list = StateInjector._adjust_array_size(moisture_list, expected_layers)

                updated_model.soilmoisture.thetai = moisture_list
                logging.info(f"Injected soil moisture with {len(moisture_list)} layers")

            # 更新相关的初始含水量参数
            if hasattr(updated_model.soilmoisture, 'swinco'):
                # 计算总初始含水量
                total_moisture = np.sum(moisture) if isinstance(moisture, np.ndarray) else sum(moisture_list)
                updated_model.soilmoisture.swinco = float(total_moisture)

            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject soil moisture: {e}")
            return model

    @staticmethod
    def _adjust_array_size(array: list, target_size: int) -> list:
        """调整数组大小以匹配目标层数"""
        current_size = len(array)

        if current_size == target_size:
            return array
        elif current_size < target_size:
            # 扩展数组，使用最后一个值填充
            last_value = array[-1] if array else 0.3  # 默认含水量
            return array + [last_value] * (target_size - current_size)
        else:
            # 截断数组
            return array[:target_size]

    @staticmethod
    def inject_crop_state(model: 'Model', lai: Optional[float] = None,
                         biomass: Optional[float] = None,
                         root_depth: Optional[float] = None) -> 'Model':
        """注入作物状态变量

        Args:
            model: 模型对象
            lai: 叶面积指数
            biomass: 生物量
            root_depth: 根深

        Returns:
            更新后的模型对象
        """
        try:
            updated_model = model.model_copy(deep=True)

            # 检查模型是否有作物组件
            if not hasattr(updated_model, 'crop') or updated_model.crop is None:
                logging.warning("Model does not have crop component")
                return updated_model

            # 注入LAI
            if lai is not None:
                updated_model = StateInjector._inject_lai(updated_model, lai)

            # 注入生物量
            if biomass is not None:
                updated_model = StateInjector._inject_biomass(updated_model, biomass)

            # 注入根深
            if root_depth is not None:
                updated_model = StateInjector._inject_root_depth(updated_model, root_depth)

            logging.info(f"Injected crop states: LAI={lai}, Biomass={biomass}, Root={root_depth}")
            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject crop state: {e}")
            return model

    @staticmethod
    def _inject_lai(model: 'Model', lai: float) -> 'Model':
        """注入LAI值"""
        try:
            # 根据pyAHC的具体结构来实现
            # 这里需要根据实际的作物组件结构来调整

            if hasattr(model.crop, 'leaf_area_index'):
                try:
                    model.crop.leaf_area_index = float(lai)
                except Exception as validation_error:
                    logging.warning(f"LAI validation failed: {validation_error}, keeping original value")
                    # 保持原始值，不修改
                    pass
            elif hasattr(model.crop, 'lai'):
                model.crop.lai = float(lai)
            elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                # 尝试通过作物发育设置来设置LAI
                if hasattr(model.crop.cropdev_settings, 'laiem'):
                    model.crop.cropdev_settings.laiem = float(lai)
            else:
                # 尝试通过发育阶段来设置LAI
                StateInjector._set_lai_through_development_stage(model, lai)

            return model

        except Exception as e:
            logging.error(f"Failed to inject LAI: {e}")
            return model

    @staticmethod
    def _inject_biomass(model: 'Model', biomass: float) -> 'Model':
        """注入生物量值"""
        try:
            if hasattr(model.crop, 'biomass'):
                model.crop.biomass = float(biomass)
            elif hasattr(model.crop, 'dry_matter'):
                model.crop.dry_matter = float(biomass)
            elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                if hasattr(model.crop.cropdev_settings, 'tdwi'):
                    model.crop.cropdev_settings.tdwi = float(biomass)
            else:
                logging.warning("Could not find biomass attribute in crop component")

            return model

        except Exception as e:
            logging.error(f"Failed to inject biomass: {e}")
            return model

    @staticmethod
    def _inject_root_depth(model: 'Model', root_depth: float) -> 'Model':
        """注入根深值"""
        try:
            if hasattr(model.crop, 'root_depth'):
                model.crop.root_depth = float(root_depth)
            elif hasattr(model.crop, 'rooting_depth'):
                model.crop.rooting_depth = float(root_depth)
            elif hasattr(model.crop, 'max_rooting_depth'):
                model.crop.max_rooting_depth = float(root_depth)
            elif hasattr(model.crop, 'rds'):
                model.crop.rds = float(root_depth)
            elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                if hasattr(model.crop.cropdev_settings, 'rdi'):
                    model.crop.cropdev_settings.rdi = float(root_depth)
            else:
                logging.warning("Could not find root depth attribute in crop component")

            return model

        except Exception as e:
            logging.error(f"Failed to inject root depth: {e}")
            return model

    @staticmethod
    def _set_lai_through_development_stage(model: 'Model', target_lai: float) -> None:
        """通过调整发育阶段来设置LAI"""
        try:
            # 这是一个复杂的逻辑，需要根据作物发育模型来实现
            # 这里提供一个简化的实现框架

            if hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                # 根据目标LAI估算发育阶段
                # 这需要作物特定的LAI-发育阶段关系
                estimated_stage = StateInjector._estimate_development_stage_from_lai(target_lai)

                if hasattr(model.crop.cropdev_settings, 'stage'):
                    model.crop.cropdev_settings.stage = estimated_stage
                elif hasattr(model.crop.cropdev_settings, 'development_stage'):
                    model.crop.cropdev_settings.development_stage = estimated_stage

        except Exception as e:
            logging.error(f"Failed to set LAI through development stage: {e}")

    @staticmethod
    def _estimate_development_stage_from_lai(lai: float) -> float:
        """从LAI估算发育阶段"""
        # 这是一个简化的线性关系，实际应该根据作物类型和品种来调整
        if lai <= 0.5:
            return 0.2  # 苗期
        elif lai <= 2.0:
            return 0.5  # 营养生长期
        elif lai <= 4.0:
            return 0.7  # 快速生长期
        elif lai <= 6.0:
            return 0.9  # 生殖生长期
        else:
            return 1.0  # 成熟期

    @staticmethod
    def inject_groundwater_level(model: 'Model', gw_level: float) -> 'Model':
        """注入地下水位

        Args:
            model: 模型对象
            gw_level: 地下水位值（cm）

        Returns:
            更新后的模型对象
        """
        try:
            updated_model = model.model_copy(deep=True)

            # 检查模型是否有底边界条件组件
            if hasattr(updated_model, 'bottomboundary') and updated_model.bottomboundary is not None:
                # 更新地下水位相关参数
                if hasattr(updated_model.bottomboundary, 'gwlevel_data'):
                    # 更新地下水位数据表格
                    # 假设我们要更新所有日期的地下水位为相同值
                    if updated_model.bottomboundary.gwlevel_data:
                        updated_data = []
                        for day, month, _ in updated_model.bottomboundary.gwlevel_data:
                            updated_data.append((day, month, float(gw_level)))
                        updated_model.bottomboundary.gwlevel_data = updated_data
                    else:
                        # 如果没有现有数据，创建一个简单的年度数据
                        updated_model.bottomboundary.gwlevel_data = [(1, 1, float(gw_level)), (31, 12, float(gw_level))]

                # 检查其他可能的地下水位属性
                elif hasattr(updated_model.bottomboundary, 'groundwater_level'):
                    updated_model.bottomboundary.groundwater_level = float(gw_level)
                elif hasattr(updated_model.bottomboundary, 'water_table_depth'):
                    # 转换为深度（通常是负值）
                    updated_model.bottomboundary.water_table_depth = abs(float(gw_level))
                elif hasattr(updated_model.bottomboundary, 'gwlevel'):
                    updated_model.bottomboundary.gwlevel = float(gw_level)
                else:
                    logging.warning("Could not find groundwater level attribute in bottom boundary")

            # 检查是否有水文组件
            elif hasattr(updated_model, 'hydrology') and updated_model.hydrology is not None:
                if hasattr(updated_model.hydrology, 'groundwater_level'):
                    updated_model.hydrology.groundwater_level = float(gw_level)
                elif hasattr(updated_model.hydrology, 'water_table'):
                    updated_model.hydrology.water_table = float(gw_level)

            else:
                logging.warning("Model does not have groundwater components")

            logging.info(f"Injected groundwater level: {gw_level} cm")
            return updated_model

        except Exception as e:
            logging.error(f"Failed to inject groundwater level: {e}")
            return model

    @staticmethod
    def _validate_injection_result(model: 'Model', injected_states: Dict[str, Any]) -> bool:
        """验证注入结果

        Args:
            model: 注入后的模型对象
            injected_states: 注入的状态变量

        Returns:
            验证是否成功
        """
        try:
            validation_passed = True

            # 验证土壤含水量注入
            if 'soil_moisture' in injected_states:
                if not StateInjector._verify_soil_moisture_injection(model, injected_states['soil_moisture']):
                    validation_passed = False

            # 验证作物状态注入
            crop_states = ['lai', 'biomass', 'root_depth']
            for state in crop_states:
                if state in injected_states:
                    if not StateInjector._verify_crop_state_injection(model, state, injected_states[state]):
                        validation_passed = False

            # 验证地下水位注入
            if 'groundwater_level' in injected_states:
                if not StateInjector._verify_groundwater_injection(model, injected_states['groundwater_level']):
                    validation_passed = False

            if validation_passed:
                logging.info("All state injections validated successfully")
            else:
                logging.warning("Some state injections failed validation")

            return validation_passed

        except Exception as e:
            logging.error(f"Failed to validate injection result: {e}")
            return False

    @staticmethod
    def _verify_soil_moisture_injection(model: 'Model', expected_moisture: np.ndarray) -> bool:
        """验证土壤含水量注入"""
        try:
            if hasattr(model, 'soilmoisture') and hasattr(model.soilmoisture, 'thetai'):
                injected_moisture = np.array(model.soilmoisture.thetai)
                expected_moisture = np.array(expected_moisture)

                # 检查数值是否接近
                if np.allclose(injected_moisture, expected_moisture, rtol=1e-6):
                    return True
                else:
                    logging.warning(f"Soil moisture injection mismatch: expected {expected_moisture}, got {injected_moisture}")
                    return False
            else:
                logging.warning("Cannot verify soil moisture injection: component not found")
                return False
        except Exception as e:
            logging.error(f"Failed to verify soil moisture injection: {e}")
            return False

    @staticmethod
    def _verify_crop_state_injection(model: 'Model', state_name: str, expected_value: float) -> bool:
        """验证作物状态注入"""
        try:
            if not hasattr(model, 'crop') or model.crop is None:
                return False

            # 根据状态名称检查相应的属性
            actual_value = None

            if state_name == 'lai':
                if hasattr(model.crop, 'leaf_area_index'):
                    actual_value = model.crop.leaf_area_index
                elif hasattr(model.crop, 'lai'):
                    actual_value = model.crop.lai
                elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                    if hasattr(model.crop.cropdev_settings, 'laiem'):
                        actual_value = model.crop.cropdev_settings.laiem
            elif state_name == 'biomass':
                if hasattr(model.crop, 'biomass'):
                    actual_value = model.crop.biomass
                elif hasattr(model.crop, 'dry_matter'):
                    actual_value = model.crop.dry_matter
                elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                    if hasattr(model.crop.cropdev_settings, 'tdwi'):
                        actual_value = model.crop.cropdev_settings.tdwi
            elif state_name == 'root_depth':
                if hasattr(model.crop, 'root_depth'):
                    actual_value = model.crop.root_depth
                elif hasattr(model.crop, 'rooting_depth'):
                    actual_value = model.crop.rooting_depth
                elif hasattr(model.crop, 'max_rooting_depth'):
                    actual_value = model.crop.max_rooting_depth
                elif hasattr(model.crop, 'rds'):
                    actual_value = model.crop.rds
                elif hasattr(model.crop, 'cropdev_settings') and model.crop.cropdev_settings is not None:
                    if hasattr(model.crop.cropdev_settings, 'rdi'):
                        actual_value = model.crop.cropdev_settings.rdi

            if actual_value is not None:
                if abs(float(actual_value) - float(expected_value)) < 1e-6:
                    return True
                else:
                    logging.warning(f"{state_name} injection mismatch: expected {expected_value}, got {actual_value}")
                    return False
            else:
                logging.warning(f"Cannot verify {state_name} injection: attribute not found")
                return False

        except Exception as e:
            logging.error(f"Failed to verify {state_name} injection: {e}")
            return False

    @staticmethod
    def _verify_groundwater_injection(model: 'Model', expected_level: float) -> bool:
        """验证地下水位注入"""
        try:
            actual_level = None

            if hasattr(model, 'bottomboundary') and model.bottomboundary is not None:
                if hasattr(model.bottomboundary, 'gwlevel_data') and model.bottomboundary.gwlevel_data:
                    # 检查第一个地下水位数据点
                    _, _, actual_level = model.bottomboundary.gwlevel_data[0]
                elif hasattr(model.bottomboundary, 'groundwater_level'):
                    actual_level = model.bottomboundary.groundwater_level
                elif hasattr(model.bottomboundary, 'gwlevel'):
                    actual_level = model.bottomboundary.gwlevel

            if actual_level is not None:
                if abs(float(actual_level) - float(expected_level)) < 1e-6:
                    return True
                else:
                    logging.warning(f"Groundwater level injection mismatch: expected {expected_level}, got {actual_level}")
                    return False
            else:
                logging.warning("Cannot verify groundwater level injection: attribute not found")
                return False

        except Exception as e:
            logging.error(f"Failed to verify groundwater level injection: {e}")
            return False
