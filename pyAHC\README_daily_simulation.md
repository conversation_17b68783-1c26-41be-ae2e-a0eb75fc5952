# pyAHC 单日循环模拟模块

## 概述

本模块实现了基于pyAHC的单日循环模拟功能，满足您的需求：
- 每次运行一天的模拟（如：2013-05-02 到 2013-05-03）
- 将前一天的输出状态作为下一天的输入状态
- 使用HDF5进行状态数据的持久化存储
- 集成了状态变量的提取和注入机制

## 核心功能

### 1. 单日模拟管理器 (DailySimulationManager)
- **项目管理**：基于HDF5的项目级数据组织
- **状态传递**：自动提取和注入状态变量
- **模型构建**：基于hetao_corn_2013.py的模型组件创建
- **结果处理**：解析模拟结果并提取关键状态变量

### 2. 状态变量管理
- **提取**：从模拟结果中提取土壤含水量、LAI、生物量等状态
- **注入**：将前一天的状态注入到当天的模型输入中
- **存储**：使用HDF5格式持久化存储状态数据

### 3. 工作流程集成
- **日循环**：支持连续多日的循环模拟
- **错误处理**：完善的异常处理和日志记录
- **进度跟踪**：实时显示模拟进度和状态

## 文件结构

```
pyAHC/
├── daily_simulation.py           # 核心模拟模块
├── daily_simulation_example.py   # 使用示例
├── README_daily_simulation.md    # 本文档
└── pyahc/db/hdf5/               # HDF5数据管理模块
    ├── manager.py               # HDF5数据管理器
    ├── state_extractor.py       # 状态变量提取器
    ├── state_injector.py        # 状态变量注入器
    └── ...
```

## 使用方法

### 1. 基本使用

```python
import datetime
from daily_simulation import DailySimulationManager

# 配置参数
config = {
    'project_name': 'hetao_daily_2013',
    'hdf5_path': './daily_simulation_2013.h5',
    'output_dir': './temp_daily_sim',
    'latitude': 45.75,
    'altitude': 1039.3
}

# 创建模拟管理器
sim_manager = DailySimulationManager(config)

# 初始化项目
sim_manager.initialize_project()

# 运行单日模拟
current_date = datetime.date(2013, 5, 2)
success = sim_manager.run_single_day_simulation(current_date)
```

### 2. 按您的需求运行

```python
# 按照您的需求，逐日运行模拟
simulation_dates = [
    datetime.date(2013, 5, 2),  # 第1天
    datetime.date(2013, 5, 3),  # 第2天，使用第1天的输出状态
    datetime.date(2013, 5, 4),  # 第3天，使用第2天的输出状态
    datetime.date(2013, 5, 5),  # 第4天，使用第3天的输出状态
    # ... 以此类推
]

for current_date in simulation_dates:
    success = sim_manager.run_single_day_simulation(current_date)
    if success:
        print(f"✓ {current_date} 模拟成功")
    else:
        print(f"✗ {current_date} 模拟失败")
```

### 3. 连续模拟

```python
# 运行连续的日循环模拟
start_date = datetime.date(2013, 5, 2)
end_date = datetime.date(2013, 5, 10)

success = sim_manager.run_daily_sequence(start_date, end_date)
```

## 运行示例

### 方法1：直接运行示例脚本

```bash
cd pyAHC
python daily_simulation_example.py
```

### 方法2：交互式选择

```bash
python daily_simulation_example.py
# 选择运行模式:
# 1. 单日循环模拟（按您的需求）
# 2. 连续日循环模拟
# 请输入选择 (1/2，默认1): 1
```

## 配置参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| project_name | str | 是 | 项目名称，用于HDF5中的数据组织 |
| hdf5_path | str | 是 | HDF5文件路径，存储所有状态数据 |
| output_dir | str | 是 | 临时输出目录，存储模拟过程文件 |
| latitude | float | 是 | 纬度信息 |
| altitude | float | 是 | 海拔信息 |

## 状态变量

模块自动管理以下状态变量：

- **土壤含水量** (soil_moisture): 各土层的含水量数组
- **叶面积指数** (lai): 作物叶面积指数
- **生物量** (biomass): 作物干物质重量
- **根深** (root_depth): 作物根系深度
- **地下水位** (groundwater_level): 地下水位深度

## 数据存储结构

HDF5文件采用以下层次结构：

```
project_name/
├── metadata/                    # 项目元数据
├── day_05-02/                  # 2013年5月2日数据
│   ├── input/                  # 输入数据
│   │   └── model_object        # 序列化的模型对象
│   ├── output/                 # 输出数据
│   │   └── result_object       # 序列化的结果对象
│   └── state-parameter_variables/  # 状态变量
│       ├── soil_moisture       # 土壤含水量
│       ├── lai                 # 叶面积指数
│       └── ...                 # 其他状态变量
├── day_05-03/                  # 2013年5月3日数据
└── ...
```

## 注意事项

1. **依赖关系**：确保已安装所有必需的依赖包（h5py, numpy, pandas等）
2. **数据文件**：确保data/目录下的数据文件完整
3. **权限**：确保对输出目录和HDF5文件有读写权限
4. **内存**：长期模拟可能需要较大内存，建议监控内存使用
5. **错误处理**：模拟失败时会记录详细日志，便于调试

## 故障排除

### 常见问题

1. **导入错误**：检查Python路径和依赖包安装
2. **文件权限**：确保对工作目录有写权限
3. **数据缺失**：检查data/目录下的数据文件
4. **内存不足**：减少模拟天数或增加系统内存

### 日志文件

模拟过程会生成详细的日志文件：
- `daily_simulation.log`：详细的运行日志
- 控制台输出：实时进度和状态信息

## 扩展功能

### 自定义状态变量

可以通过修改StateExtractor和StateInjector来支持更多状态变量：

```python
# 在StateExtractor中添加新的提取方法
@staticmethod
def extract_custom_variable(result):
    # 自定义提取逻辑
    pass

# 在StateInjector中添加新的注入方法
@staticmethod
def inject_custom_variable(model, value):
    # 自定义注入逻辑
    pass
```

### 数据同化集成

模块预留了数据同化接口，可以集成观测数据：

```python
# 在run_single_day_simulation中添加数据同化步骤
if enable_assimilation:
    # 加载观测数据
    observations = load_observations(current_date)
    # 执行数据同化
    updated_states = assimilate_data(current_states, observations)
    # 保存同化后的状态
    current_states = updated_states
```

## 联系方式

如有问题或建议，请联系开发团队。