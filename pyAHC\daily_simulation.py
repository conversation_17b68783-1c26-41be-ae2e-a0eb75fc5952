#!/usr/bin/env python3
"""单日循环模拟模块

基于hetao_corn_2013.py的流程，实现单日模拟功能：
- 每次运行一天的模拟
- 将前一天的输出状态作为下一天的输入状态
- 支持状态变量的提取和注入
- 集成HDF5数据管理

主要功能：
1. 单日模拟执行
2. 状态变量提取和注入
3. 日期递增管理
4. HDF5数据存储
"""

import subprocess
import datetime
import logging
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

# 导入pyAHC核心组件
from pyahc.model.model import Model, ModelBuilder
from pyahc.components.ctr import CtrFile as _CtrFile
from pyahc.components.metadata import Metadata
from pyahc.components.simsettings import GeneralSettings
from pyahc.components.meteorology import Meteorology, MetFile
from pyahc.components.crop import Crop, CropFile
from pyahc.components.irrigation import FixedIrrigation
from pyahc.components.soilwater import SoilMoisture, SurfaceFlow, Evaporation, SoilProfile, SnowAndFrost
from pyahc.components.simsettings import RichardsSettings
from pyahc.components.drainage import Drainage, DraFile
from pyahc.components.boundary import BottomBoundary as _BottomBoundary
from pyahc.components.transport import HeatFlow, SoluteTransport
from pyahc.components.epic_crop import EpicCrop as _EpicCrop
from pyahc.components.observation import ObservationPoints as _ObservationPoints

# 导入HDF5组件
from pyahc.db.hdf5.manager import ProjectHDF5Manager
from pyahc.db.hdf5.state_extractor import StateExtractor
from pyahc.db.hdf5.state_injector import StateInjector

# 导入数据文件
from data.gwlevel_data import GWLEVEL_DATA
from data.hbot5_data import HBOT5_DATA
from data.gwc_data import GWC_DATA
from data.thetai_data import STANDARD_THETAI
from data.maize_default import MAIZE_DEFAULT_PARAMS
from data.meteorological_data import MeteorologicalDataManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DailySimulationManager:
    """单日循环模拟管理器"""

    def __init__(self, base_config: Dict[str, Any]):
        """初始化单日模拟管理器

        Args:
            base_config: 基础配置字典，包含：
                - project_name: 项目名称
                - hdf5_path: HDF5文件路径
                - output_dir: 输出目录
                - latitude: 纬度
                - altitude: 海拔
        """
        self.base_config = base_config
        self.project_name = base_config.get('project_name', 'hetao')
        self.hdf5_path = base_config.get('hdf5_path', 'daily_simulation.h5')
        self.output_dir = Path(base_config.get('output_dir', './temp_daily_sim'))
        self.latitude = base_config.get('latitude', 45.75)
        self.altitude = base_config.get('altitude', 1039.3)

        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)

        # 初始化HDF5管理器
        self.hdf5_manager = ProjectHDF5Manager(self.hdf5_path)

        # 初始化气象数据管理器
        self.meteo_manager = MeteorologicalDataManager()

        logger.info(f"初始化单日模拟管理器: {self.project_name}")
        logger.info(f"HDF5文件: {self.hdf5_path}")
        logger.info(f"输出目录: {self.output_dir}")

    def initialize_project(self):
        """初始化HDF5项目"""
        try:
            if not self.hdf5_manager.project_exists(self.project_name):
                metadata = {
                    'model_info': {
                        'pyahc_version': '0.1.0',
                        'ahc_version': 'V201',
                        'creation_date': datetime.datetime.now().isoformat(),
                        'created_by': 'DailySimulationManager'
                    },
                    'location_info': {
                        'latitude': self.latitude,
                        'altitude': self.altitude
                    },
                    'crop_info': {
                        'crop_type': 'corn',
                        'variety': 'maize'
                    }
                }

                with self.hdf5_manager:
                    self.hdf5_manager.create_project(self.project_name, metadata)
                logger.info(f"项目初始化完成: {self.project_name}")
            else:
                logger.info(f"项目已存在: {self.project_name}")
        except Exception as e:
            logger.error(f"项目初始化失败: {e}")
            raise

    def create_base_model(self, start_date: datetime.date, end_date: datetime.date) -> Tuple[Model, _EpicCrop, _ObservationPoints]:
        """创建基础模型组件

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            tuple: (Model对象, EpicCrop对象, ObservationPoints对象)
        """
        logger.info(f"创建基础模型: {start_date} 到 {end_date}")

        # === 核心控制组件 ===
        ctr_file = _CtrFile.from_config({
            'project': self.project_name,
            'ssrun': [start_date.day, start_date.month, start_date.year],
            'esrun': [end_date.day, end_date.month, end_date.year],
            'metfil': 'weather.013',
            'lat': self.latitude,
            'alt': self.altitude,
            'calfil': 'Maize',
            'swcrop': 1,
            'swdra': 1,
            'dramet': 1,
            'swsolu': 1,
            'swsalt': 1,
            'swhea': 1,
            'swvap': 1,
            'swdivd': 1,
            'swdisper': 1,
            'swdispersion': 1,
            'swmacro': 1,
            'swbbcfile': 0,
            'swbotb': 6,
            'gwlevel_data': GWLEVEL_DATA,
            'hbot5_data': HBOT5_DATA,
            'gwc_data': GWC_DATA
        })

        # === 基础设置组件 ===
        metadata = Metadata()
        general_settings = GeneralSettings(
            project=self.project_name,
            pathwork=str(self.output_dir),
            swscre=0,
            swerror=1,
            extensions=["csv"],
            tstart=start_date,
            tend=end_date
        )

        # === 气象组件 ===
        meteorology = Meteorology(
            metfile=MetFile(
                metfil='weather.013',
                content=self.meteo_manager.to_dataframe(end_date=end_date)
            )
        )

        # === 作物组件 ===
        crop = Crop(
            swcrop=1,
            cropfiles={
                'Maize.CAL': CropFile(crp=MAIZE_DEFAULT_PARAMS)
            }
        )

        # === 土壤水分组件 ===
        soilmoisture = SoilMoisture(
            swinco=0,
            thetai=STANDARD_THETAI
        )

        # === 其他组件 ===
        fixedirrigation = FixedIrrigation(swirfix=0)
        surfaceflow = SurfaceFlow()
        evaporation = Evaporation()
        soilprofile = SoilProfile()
        snowandfrost = SnowAndFrost(swsnow=0, swfrost=0)
        richards = RichardsSettings(swkmean=1, swkimpl=0)

        # === 排水组件 ===
        lateraldrainage = Drainage(
            swdra=1,
            drafile=DraFile()
        )

        # === 底边界组件 ===
        bottomboundary = _BottomBoundary(
            swbbcfile=0,
            swbotb=6,
            gwlevel_data=GWLEVEL_DATA,
            hbot5_data=HBOT5_DATA,
            gwc_data=GWC_DATA
        )

        # === 传输组件 ===
        heatflow = HeatFlow(swhea=1)
        solutetransport = SoluteTransport(swsolu=1)

        # === 创建模型 ===
        model = Model(
            metadata=metadata,
            ctr_file=ctr_file,
            generalsettings=general_settings,
            meteorology=meteorology,
            crop=crop,
            fixedirrigation=fixedirrigation,
            soilmoisture=soilmoisture,
            surfaceflow=surfaceflow,
            evaporation=evaporation,
            soilprofile=soilprofile,
            snowandfrost=snowandfrost,
            richards=richards,
            lateraldrainage=lateraldrainage,
            bottomboundary=bottomboundary,
            heatflow=heatflow,
            solutetransport=solutetransport
        )

        # === 创建EPIC作物组件 ===
        epic_crop = _EpicCrop.from_config(MAIZE_DEFAULT_PARAMS)

        # === 创建观测点组件 ===
        obs_points = _ObservationPoints()

        logger.info("基础模型创建完成")
        return model, epic_crop, obs_points

    def inject_previous_states(self, model: Model, previous_date: datetime.date) -> Model:
        """注入前一天的状态变量

        Args:
            model: 当前模型对象
            previous_date: 前一天日期

        Returns:
            注入状态后的模型对象
        """
        try:
            # 从HDF5加载前一天的状态
            with self.hdf5_manager:
                daily_data = self.hdf5_manager.load_daily_data(self.project_name, previous_date)
                previous_states = daily_data.get('states', {})

            if previous_states:
                logger.info(f"加载前一天状态: {previous_date}, 状态变量数量: {len(previous_states)}")
                # 使用StateInjector注入状态
                updated_model = StateInjector.inject_all_states(model, previous_states)
                logger.info("状态注入完成")
                return updated_model
            else:
                logger.info(f"未找到前一天状态: {previous_date}, 使用初始状态")
                return model

        except Exception as e:
            logger.warning(f"加载前一天状态失败: {e}, 使用初始状态")
            return model

    def _generate_input_files(self, model: Model, epic_crop: _EpicCrop, obs_points: _ObservationPoints):
        """生成输入文件"""
        logger.info("生成输入文件...")

        # 使用 ModelBuilder 生成主要文件
        builder = ModelBuilder(model, str(self.output_dir))

        # 1. 复制可执行文件
        builder.copy_executable()
        logger.info("✓ 可执行文件复制完成")

        # 2. 生成所有输入文件
        builder.write_inputs()
        logger.info("✓ 所有输入文件生成完成")

        # 3. 生成 Maize.CRE 文件（EPIC作物模型输入）
        maize_cre_path = self.output_dir / "Maize.CRE"
        epic_crop.write_epic_crop(str(maize_cre_path))
        logger.info("✓ Maize.CRE 文件生成完成")

        # 4. 生成 point.OBS 文件（土壤分层输入）
        obs_points.write_observation_points(str(self.output_dir))
        logger.info("✓ point.OBS 文件生成完成")

    def _run_ahc_executable(self) -> bool:
        """运行AHC可执行文件"""
        logger.info("运行AHC可执行文件...")

        try:
            # 查找可执行文件
            exe_files = list(self.output_dir.glob("*.exe"))
            if not exe_files:
                logger.error("未找到AHC可执行文件")
                return False

            exe_path = exe_files[0]
            logger.info(f"找到可执行文件: {exe_path.name}")

            # 运行可执行文件
            result = subprocess.run(
                [str(exe_path)],
                cwd=str(self.output_dir),
                capture_output=True,
                text=True,
                timeout=60  # 60秒超时
            )

            logger.info(f"返回码: {result.returncode}")
            if result.stdout:
                logger.info(f"标准输出: {result.stdout[:200]}...")
            if result.stderr:
                logger.warning(f"标准错误: {result.stderr[:200]}...")

            # 检查是否成功运行
            success = result.returncode == 0 and "normal completion" in result.stdout
            if success:
                logger.info("✓ AHC执行成功")
            else:
                logger.error(f"✗ AHC执行失败，返回码: {result.returncode}")

            # 清理可执行文件
            try:
                exe_path.unlink()
                logger.info("✓ 可执行文件已清理")
            except Exception as e:
                logger.warning(f"清理可执行文件失败: {e}")

            return success

        except subprocess.TimeoutExpired:
            logger.error("✗ AHC执行超时")
            return False
        except Exception as e:
            logger.error(f"✗ AHC执行过程中发生错误: {e}")
            return False

    def _parse_simulation_result(self):
        """解析模拟结果"""
        logger.info("解析模拟结果...")

        try:
            # 这里需要根据实际的结果解析逻辑来实现
            # 由于我们没有实际的Result类实现，这里提供一个简化版本

            # 检查输出文件是否存在
            output_files = list(self.output_dir.glob("rs0.*"))
            if not output_files:
                logger.error("未找到输出文件")
                return None

            logger.info(f"找到输出文件: {[f.name for f in output_files]}")

            # 创建一个简化的结果对象
            class SimpleResult:
                def __init__(self):
                    self.output = {}
                    self.csv = None
                    self.ascii = {}

                def load_csv_output(self, output_dir):
                    """加载CSV输出"""
                    csv_files = list(Path(output_dir).glob("*.csv"))
                    if csv_files:
                        import pandas as pd
                        try:
                            self.csv = pd.read_csv(csv_files[0])
                            logger.info(f"加载CSV文件: {csv_files[0].name}")
                        except Exception as e:
                            logger.warning(f"加载CSV文件失败: {e}")

                def load_ascii_output(self, output_dir):
                    """加载ASCII输出"""
                    ascii_files = list(Path(output_dir).glob("rs0.*"))
                    for file in ascii_files:
                        try:
                            with open(file, 'r', encoding='utf-8', errors='ignore') as f:
                                self.ascii[file.suffix] = f.read()
                            logger.info(f"加载ASCII文件: {file.name}")
                        except Exception as e:
                            logger.warning(f"加载ASCII文件失败 {file.name}: {e}")

            # 创建结果对象并加载数据
            result = SimpleResult()
            result.load_csv_output(self.output_dir)
            result.load_ascii_output(self.output_dir)

            logger.info("✓ 结果解析完成")
            return result

        except Exception as e:
            logger.error(f"结果解析失败: {e}")
            return None

    def _save_daily_data(self, current_date: datetime.date, model: Model, result, current_states: Dict[str, Any]):
        """保存日数据到HDF5"""
        logger.info(f"保存日数据: {current_date}")

        try:
            with self.hdf5_manager:
                self.hdf5_manager.save_daily_data(
                    self.project_name,
                    current_date,
                    model,
                    result,
                    current_states
                )
            logger.info("✓ 日数据保存完成")
        except Exception as e:
            logger.error(f"保存日数据失败: {e}")
            raise

    def run_single_day_simulation(self, current_date: datetime.date) -> bool:
        """运行单日模拟

        Args:
            current_date: 当前模拟日期

        Returns:
            bool: 模拟是否成功
        """
        logger.info(f"开始单日模拟: {current_date}")

        try:
            # 1. 创建单日模型（当天开始，当天结束）
            start_date = current_date
            end_date = current_date

            model, epic_crop, obs_points = self.create_base_model(start_date, end_date)

            # 2. 注入前一天的状态（如果存在）
            previous_date = current_date - datetime.timedelta(days=1)
            model = self.inject_previous_states(model, previous_date)

            # 3. 生成输入文件
            self._generate_input_files(model, epic_crop, obs_points)

            # 4. 运行AHC模拟
            success = self._run_ahc_executable()

            if success:
                # 5. 解析结果并提取状态
                result = self._parse_simulation_result()
                if result:
                    # 6. 提取状态变量
                    current_states = StateExtractor.extract_all_states(result)

                    # 7. 保存到HDF5
                    self._save_daily_data(current_date, model, result, current_states)

                    logger.info(f"✓ 单日模拟成功完成: {current_date}")
                    return True
                else:
                    logger.error(f"结果解析失败: {current_date}")
                    return False
            else:
                logger.error(f"AHC执行失败: {current_date}")
                return False

        except Exception as e:
            logger.error(f"单日模拟失败: {current_date}, 错误: {e}")
            return False

    def run_daily_sequence(self, start_date: datetime.date, end_date: datetime.date) -> bool:
        """运行日循环模拟序列

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            bool: 整个序列是否成功
        """
        logger.info(f"开始日循环模拟序列: {start_date} 到 {end_date}")

        # 初始化项目
        self.initialize_project()

        current_date = start_date
        success_count = 0
        total_days = (end_date - start_date).days + 1

        while current_date <= end_date:
            logger.info(f"模拟进度: {success_count + 1}/{total_days}")

            success = self.run_single_day_simulation(current_date)
            if success:
                success_count += 1
                logger.info(f"✓ 第 {success_count} 天模拟成功: {current_date}")
            else:
                logger.error(f"✗ 第 {success_count + 1} 天模拟失败: {current_date}")
                # 可以选择继续或停止
                # return False  # 如果要在失败时停止

            current_date += datetime.timedelta(days=1)

        logger.info(f"日循环模拟序列完成: 成功 {success_count}/{total_days} 天")
        return success_count == total_days


def run_daily_simulation_example():
    """运行单日循环模拟示例"""

    # 配置参数
    config = {
        'project_name': 'hetao_daily_2013',
        'hdf5_path': './daily_simulation_2013.h5',
        'output_dir': './temp_daily_sim',
        'latitude': 45.75,
        'altitude': 1039.3
    }

    # 创建模拟管理器
    sim_manager = DailySimulationManager(config)

    # 按照您的需求运行单日模拟
    dates_to_simulate = [
        (datetime.date(2013, 5, 2), datetime.date(2013, 5, 3)),
        (datetime.date(2013, 5, 3), datetime.date(2013, 5, 4)),
        (datetime.date(2013, 5, 4), datetime.date(2013, 5, 5)),
        (datetime.date(2013, 5, 5), datetime.date(2013, 5, 6)),
        (datetime.date(2013, 5, 6), datetime.date(2013, 5, 7)),
    ]

    logger.info("开始按日期序列运行模拟...")

    for i, (start_date, end_date) in enumerate(dates_to_simulate, 1):
        logger.info(f"\n=== 第 {i} 次模拟: {start_date} ===")

        # 运行单日模拟（实际上start_date和end_date是同一天）
        success = sim_manager.run_single_day_simulation(start_date)

        if success:
            logger.info(f"✓ 第 {i} 次模拟成功")
        else:
            logger.error(f"✗ 第 {i} 次模拟失败")
            break

    logger.info("单日循环模拟示例完成")


def run_continuous_simulation_example():
    """运行连续日循环模拟示例"""

    # 配置参数
    config = {
        'project_name': 'hetao_continuous_2013',
        'hdf5_path': './continuous_simulation_2013.h5',
        'output_dir': './temp_continuous_sim',
        'latitude': 45.75,
        'altitude': 1039.3
    }

    # 创建模拟管理器
    sim_manager = DailySimulationManager(config)

    # 运行连续模拟
    start_date = datetime.date(2013, 5, 2)
    end_date = datetime.date(2013, 5, 10)  # 模拟9天

    success = sim_manager.run_daily_sequence(start_date, end_date)

    if success:
        logger.info("✓ 连续日循环模拟成功完成")
    else:
        logger.error("✗ 连续日循环模拟失败")


if __name__ == "__main__":
    print("=== pyAHC 单日循环模拟模块 ===")
    print("1. 运行单日循环模拟示例")
    print("2. 运行连续日循环模拟示例")

    choice = input("请选择运行模式 (1/2): ").strip()

    if choice == "1":
        run_daily_simulation_example()
    elif choice == "2":
        run_continuous_simulation_example()
    else:
        print("无效选择，运行默认的单日循环模拟示例")
        run_daily_simulation_example()