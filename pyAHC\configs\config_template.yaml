# pyAHC HDF5工作流程配置模板
# 
# 这个文件提供了运行完整HDF5集成工作流程所需的所有配置选项。
# 复制此文件并根据您的具体需求进行修改。

# ==================== 基本项目信息 ====================

# HDF5文件路径（必需）
# 存储所有模拟数据的HDF5文件路径
hdf5_path: "project_simulation.h5"

# 项目名称（必需）
# 用于在HDF5文件中标识项目的唯一名称
# 建议格式：作物_地块_年份，如 "corn_001-2023"
project_name: "corn_field_001_2023"

# ==================== 模拟时间设置 ====================

# 模拟开始日期（必需）
# 格式：YYYY-MM-DD
start_date: "2023-05-01"

# 模拟结束日期（必需）
# 格式：YYYY-MM-DD
end_date: "2023-09-30"

# 作物种植日期（可选）
# 格式：YYYY-MM-DD，如果不提供则使用空值
planting_date: "2023-05-01"

# ==================== 地块信息 ====================

# 地块ID（可选，默认：'unknown'）
# 用于标识具体地块的唯一标识符
field_id: "field_001"

# 作物类型（可选，默认：'unknown'）
# 如：corn, wheat, soybean, rice等
crop_type: "corn"

# 作物品种（可选，默认：'unknown'）
# 具体的作物品种名称
variety: "Pioneer_1234"

# ==================== 地理位置信息 ====================

# 纬度（可选）
# 十进制度数，北纬为正，南纬为负
latitude: 40.7128

# 经度（可选）
# 十进制度数，东经为正，西经为负
longitude: -74.0060

# 海拔高度（可选）
# 单位：米
altitude: 10.0

# ==================== 模拟控制选项 ====================

# 启用数据同化（可选，默认：false）
# 是否在模拟过程中应用数据同化
enable_assimilation: false

# 遇到错误时停止（可选，默认：true）
# true：遇到错误立即停止模拟
# false：跳过错误日期，继续模拟
stop_on_error: true

# 保存中间结果（可选，默认：true）
# 是否保存每日的中间模拟结果
save_intermediate: true

# ==================== 高级配置选项 ====================

# 模型特定参数（可选）
# 这些参数将传递给模型创建函数
model_parameters:
  # 土壤参数
  soil:
    # 土壤类型
    soil_type: "loam"
    # 田间持水量
    field_capacity: 0.35
    # 凋萎点
    wilting_point: 0.15
    # 饱和导水率 (cm/day)
    saturated_conductivity: 10.0
  
  # 作物参数
  crop:
    # 最大叶面积指数
    max_lai: 6.0
    # 最大根深 (cm)
    max_root_depth: 120.0
    # 作物系数
    crop_coefficient: 1.2
  
  # 气象参数
  meteorology:
    # 参考蒸散发计算方法
    et_method: "penman_monteith"
    # 风速高度 (m)
    wind_height: 2.0

# 数据同化配置（可选）
# 仅在enable_assimilation为true时使用
assimilation:
  # 观测数据文件路径
  observation_file: "observations.csv"
  
  # 同化变量列表
  assimilate_variables:
    - "lai"
    - "soil_moisture"
    - "biomass"
  
  # 同化方法
  method: "enkf"  # enkf, 3dvar, 4dvar
  
  # 集合大小（仅用于EnKF）
  ensemble_size: 50
  
  # 观测误差标准差
  observation_errors:
    lai: 0.5
    soil_moisture: 0.05
    biomass: 200.0

# 输出控制（可选）
output:
  # 输出变量列表
  variables:
    - "lai"
    - "biomass"
    - "soil_moisture"
    - "root_depth"
    - "groundwater_level"
    - "evapotranspiration"
  
  # 输出频率
  frequency: "daily"  # daily, weekly, monthly
  
  # 生成汇总统计
  generate_summary: true
  
  # 生成时间序列图
  generate_plots: false

# 计算资源配置（可选）
computing:
  # 并行处理
  parallel: false
  
  # 进程数（仅在parallel为true时使用）
  num_processes: 4
  
  # 内存限制 (GB)
  memory_limit: 8.0
  
  # 临时文件目录
  temp_directory: "./temp"

# 日志配置（可选）
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # 日志文件路径（如果不提供则只输出到控制台）
  file: "simulation.log"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# ==================== 示例配置说明 ====================

# 1. 最小配置示例：
#    只需要提供必需的参数即可运行基本模拟
#
#    hdf5_path: "my_simulation.h5"
#    project_name: "my_project"
#    start_date: "2023-05-01"
#    end_date: "2023-05-31"

# 2. 完整配置示例：
#    包含所有可选参数的完整配置
#    （如上面的完整配置）

# 3. 数据同化配置示例：
#    启用数据同化功能的配置
#
#    enable_assimilation: true
#    assimilation:
#      observation_file: "field_observations.csv"
#      method: "enkf"
#      ensemble_size: 100

# ==================== 使用说明 ====================

# 1. 复制此文件并重命名为您的项目配置文件
# 2. 根据您的具体需求修改配置参数
# 3. 在Python代码中加载配置：
#
#    import yaml
#    with open('your_config.yaml', 'r') as f:
#        config = yaml.safe_load(f)
#
# 4. 运行模拟：
#
#    from pyahc.db.hdf5.workflow import run_project_simulation
#    manager = run_project_simulation(config)

# ==================== 注意事项 ====================

# 1. 日期格式必须为 YYYY-MM-DD
# 2. 文件路径可以是相对路径或绝对路径
# 3. 项目名称在同一个HDF5文件中必须唯一
# 4. 模拟期间不应超过一年（建议）
# 5. 确保有足够的磁盘空间存储HDF5文件
